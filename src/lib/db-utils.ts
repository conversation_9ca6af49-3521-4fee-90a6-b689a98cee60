import { prisma } from './prisma';

// 数据库连接管理工具
export class DatabaseManager {
  private static instance: DatabaseManager;
  private connectionCount = 0;
  private maxConnections = 3; // 大幅减少最大连接数
  private queue: Array<{ resolve: Function; reject: Function; operation: () => Promise<any> }> = [];
  private processing = false;

  private constructor() {}

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  // 执行数据库操作，使用队列管理
  async executeWithConnection<T>(operation: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push({ resolve, reject, operation });
      this.processQueue();
    });
  }

  private async processQueue() {
    if (this.processing || this.queue.length === 0) {
      return;
    }

    this.processing = true;

    while (this.queue.length > 0 && this.connectionCount < this.maxConnections) {
      const { resolve, reject, operation } = this.queue.shift()!;

      this.connectionCount++;

      try {
        const result = await operation();
        resolve(result);
      } catch (error) {
        reject(error);
      } finally {
        this.connectionCount--;
      }
    }

    this.processing = false;

    // 如果还有队列中的任务，继续处理
    if (this.queue.length > 0) {
      setTimeout(() => this.processQueue(), 10);
    }
  }

  // 批量执行操作，限制并发数
  async executeBatch<T>(operations: (() => Promise<T>)[], batchSize: number = 3): Promise<T[]> {
    const results: T[] = [];
    
    for (let i = 0; i < operations.length; i += batchSize) {
      const batch = operations.slice(i, i + batchSize);
      const batchResults = await Promise.all(
        batch.map(op => this.executeWithConnection(op))
      );
      results.push(...batchResults);
    }
    
    return results;
  }

  // 获取连接状态
  getConnectionStatus(): { current: number; max: number } {
    return {
      current: this.connectionCount,
      max: this.maxConnections
    };
  }
}

// 工具函数：安全执行数据库查询
export async function safeDbQuery<T>(
  queryFn: () => Promise<T>,
  fallback: T,
  errorMessage?: string
): Promise<T> {
  try {
    const dbManager = DatabaseManager.getInstance();
    return await dbManager.executeWithConnection(queryFn);
  } catch (error) {
    console.error(errorMessage || 'Database query error:', error);
    return fallback;
  }
}

// 工具函数：安全执行事务
export async function safeDbTransaction<T>(
  transactionFn: (tx: any) => Promise<T>,
  fallback: T,
  errorMessage?: string,
  timeout: number = 10000
): Promise<T> {
  try {
    const dbManager = DatabaseManager.getInstance();
    return await dbManager.executeWithConnection(async () => {
      return await prisma.$transaction(transactionFn, { timeout });
    });
  } catch (error) {
    console.error(errorMessage || 'Database transaction error:', error);
    return fallback;
  }
}

// 工具函数：批量执行数据库操作
export async function batchDbOperations<T>(
  operations: (() => Promise<T>)[],
  batchSize: number = 3
): Promise<T[]> {
  const dbManager = DatabaseManager.getInstance();
  return await dbManager.executeBatch(operations, batchSize);
}

// 连接池健康检查
export async function checkDatabaseHealth(): Promise<{
  isHealthy: boolean;
  connectionStatus: { current: number; max: number };
  error?: string;
}> {
  try {
    const dbManager = DatabaseManager.getInstance();
    
    // 简单的健康检查查询
    await dbManager.executeWithConnection(async () => {
      await prisma.$queryRaw`SELECT 1`;
    });
    
    return {
      isHealthy: true,
      connectionStatus: dbManager.getConnectionStatus()
    };
  } catch (error) {
    return {
      isHealthy: false,
      connectionStatus: DatabaseManager.getInstance().getConnectionStatus(),
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
