import { prisma } from './prisma';
import { DatabasePerformanceMonitor } from './db-config';

// SQL查询优化工具
export class SQLOptimizer {
  
  // 优化的工具查询 - 使用索引和减少JOIN
  static async getOptimizedTools(locale: string, limit: number = 20) {
    const endTimer = DatabasePerformanceMonitor.startQuery('getOptimizedTools');
    
    try {
      // 使用原生SQL查询，更好的性能
      const result = await prisma.$queryRaw<any[]>`
        WITH tool_data AS (
          SELECT 
            t.id,
            t.tool_id,
            t.icon_url,
            t.url,
            t.pricing_type,
            t.is_premium,
            t.is_new,
            t.is_featured,
            t.rating,
            t.created_at,
            t.updated_at,
            tt.name,
            tt.description
          FROM tools t
          LEFT JOIN tool_translations tt ON t.tool_id = tt.tool_id AND tt.locale = ${locale}
          ORDER BY t.updated_at DESC
          LIMIT ${limit}
        ),
        tool_count AS (
          SELECT COUNT(*) as total FROM tools
        )
        SELECT 
          td.*,
          tc.total
        FROM tool_data td
        CROSS JOIN tool_count tc
      `;
      
      return result;
    } finally {
      endTimer();
    }
  }

  // 优化的分类工具查询
  static async getOptimizedCategoryTools(categorySlug: string, locale: string, limit: number = 8) {
    const endTimer = DatabasePerformanceMonitor.startQuery('getOptimizedCategoryTools');
    
    try {
      // 单个优化查询，减少数据库往返
      const result = await prisma.$queryRaw<any[]>`
        WITH RECURSIVE category_tree AS (
          -- 获取目标分类
          SELECT id, slug, level, parent_id, icon_url, 0 as depth
          FROM categories 
          WHERE slug = ${categorySlug}
          
          UNION ALL
          
          -- 获取子分类（如果是父分类）
          SELECT c.id, c.slug, c.level, c.parent_id, c.icon_url, ct.depth + 1
          FROM categories c
          INNER JOIN category_tree ct ON c.parent_id = ct.id
          WHERE ct.depth < 1
        ),
        category_info AS (
          SELECT 
            c.id,
            c.slug,
            c.icon_url,
            c.level,
            ct.name,
            ct.description
          FROM category_tree c
          LEFT JOIN category_translations ct ON c.id = ct.category_id AND ct.locale = ${locale}
          WHERE c.slug = ${categorySlug}
          LIMIT 1
        ),
        category_tools AS (
          SELECT DISTINCT
            t.id,
            t.tool_id,
            t.icon_url,
            t.url,
            t.pricing_type,
            t.is_premium,
            t.is_new,
            t.is_featured,
            t.rating,
            t.created_at,
            tt.name,
            tt.description
          FROM tools t
          INNER JOIN tool_categories tc ON t.tool_id = tc.tool_id
          INNER JOIN category_tree ct ON tc.category_id = ct.id
          LEFT JOIN tool_translations tt ON t.tool_id = tt.tool_id AND tt.locale = ${locale}
          ORDER BY t.updated_at DESC
          LIMIT ${limit}
        ),
        tool_count AS (
          SELECT COUNT(DISTINCT tc.tool_id) as total
          FROM tool_categories tc
          INNER JOIN category_tree ct ON tc.category_id = ct.id
        )
        SELECT 
          'category' as type,
          ci.id as category_id,
          ci.slug as category_slug,
          ci.icon_url as category_icon_url,
          ci.level as category_level,
          ci.name as category_name,
          ci.description as category_description,
          NULL as tool_id,
          NULL as tool_name,
          NULL as tool_description,
          NULL as tool_icon_url,
          NULL as tool_url,
          NULL as pricing_type,
          NULL as is_premium,
          NULL as is_new,
          NULL as is_featured,
          NULL as rating,
          NULL as created_at,
          tc.total
        FROM category_info ci
        CROSS JOIN tool_count tc
        
        UNION ALL
        
        SELECT 
          'tool' as type,
          NULL as category_id,
          NULL as category_slug,
          NULL as category_icon_url,
          NULL as category_level,
          NULL as category_name,
          NULL as category_description,
          ct.tool_id,
          ct.name as tool_name,
          ct.description as tool_description,
          ct.icon_url as tool_icon_url,
          ct.url as tool_url,
          ct.pricing_type,
          ct.is_premium,
          ct.is_new,
          ct.is_featured,
          ct.rating,
          ct.created_at,
          NULL as total
        FROM category_tools ct
      `;
      
      return result;
    } finally {
      endTimer();
    }
  }

  // 批量获取多个分类的工具（减少数据库调用）
  static async getBatchCategoryTools(categorySlugs: string[], locale: string, limit: number = 8) {
    const endTimer = DatabasePerformanceMonitor.startQuery('getBatchCategoryTools');
    
    try {
      const result = await prisma.$queryRaw<any[]>`
        WITH category_data AS (
          SELECT 
            c.id,
            c.slug,
            c.icon_url,
            c.level,
            ct.name,
            ct.description
          FROM categories c
          LEFT JOIN category_translations ct ON c.id = ct.category_id AND ct.locale = ${locale}
          WHERE c.slug = ANY(${categorySlugs})
        ),
        category_tools AS (
          SELECT 
            cd.slug as category_slug,
            t.tool_id,
            t.icon_url,
            t.url,
            t.pricing_type,
            t.is_premium,
            t.is_new,
            t.is_featured,
            t.rating,
            t.created_at,
            tt.name,
            tt.description,
            ROW_NUMBER() OVER (PARTITION BY cd.slug ORDER BY t.updated_at DESC) as rn
          FROM category_data cd
          INNER JOIN tool_categories tc ON cd.id = tc.category_id
          INNER JOIN tools t ON tc.tool_id = t.tool_id
          LEFT JOIN tool_translations tt ON t.tool_id = tt.tool_id AND tt.locale = ${locale}
        )
        SELECT 
          cd.*,
          COALESCE(
            JSON_AGG(
              JSON_BUILD_OBJECT(
                'toolId', ct.tool_id,
                'name', ct.name,
                'description', ct.description,
                'iconUrl', ct.icon_url,
                'url', ct.url,
                'pricingType', ct.pricing_type,
                'isPremium', ct.is_premium,
                'isNew', ct.is_new,
                'isFeatured', ct.is_featured,
                'rating', ct.rating,
                'createdAt', ct.created_at
              ) ORDER BY ct.created_at DESC
            ) FILTER (WHERE ct.tool_id IS NOT NULL),
            '[]'::json
          ) as tools
        FROM category_data cd
        LEFT JOIN category_tools ct ON cd.slug = ct.category_slug AND ct.rn <= ${limit}
        GROUP BY cd.id, cd.slug, cd.icon_url, cd.level, cd.name, cd.description
        ORDER BY cd.slug
      `;
      
      return result;
    } finally {
      endTimer();
    }
  }
}

// 数据库索引建议
export const indexRecommendations = {
  // 建议添加的索引
  recommended: [
    'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tools_updated_at_desc ON tools (updated_at DESC);',
    'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tool_translations_locale_tool_id ON tool_translations (locale, tool_id);',
    'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tool_categories_category_id_tool_id ON tool_categories (category_id, tool_id);',
    'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_slug_level ON categories (slug, level);',
    'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_parent_id ON categories (parent_id) WHERE parent_id IS NOT NULL;',
    'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_category_translations_locale_category_id ON category_translations (locale, category_id);',
  ],
  
  // 复合索引建议
  composite: [
    'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tools_featured_premium_updated ON tools (is_featured, is_premium, updated_at DESC);',
    'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tools_new_updated ON tools (is_new, updated_at DESC) WHERE is_new = true;',
  ],
  
  // 部分索引建议（针对常用查询）
  partial: [
    'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tools_active ON tools (updated_at DESC) WHERE is_deleted = false;',
    'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_active ON categories (slug) WHERE level IN (1, 2);',
  ]
};

// 查询性能分析工具
export function analyzeQueryPerformance() {
  const stats = DatabasePerformanceMonitor.getAllStats();
  
  console.log('=== Database Query Performance Analysis ===');
  
  Object.entries(stats).forEach(([queryId, stat]) => {
    if (stat) {
      console.log(`${queryId}:`);
      console.log(`  Average: ${stat.avg.toFixed(2)}ms`);
      console.log(`  Min: ${stat.min}ms, Max: ${stat.max}ms`);
      console.log(`  Count: ${stat.count}`);
      
      if (stat.avg > 1000) {
        console.warn(`  ⚠️  Slow query detected (avg > 1s)`);
      }
    }
  });
  
  console.log('===========================================');
}
