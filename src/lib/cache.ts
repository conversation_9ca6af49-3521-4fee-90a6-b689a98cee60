// 简单的内存缓存实现
interface CacheItem {
  data: any;
  expiry: number;
}

class MemoryCache {
  private cache: Map<string, CacheItem> = new Map();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // 每5分钟清理一次过期缓存
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  set(key: string, data: any, ttlSeconds: number): void {
    const expiry = Date.now() + (ttlSeconds * 1000);
    this.cache.set(key, { data, expiry });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) {
      return null;
    }

    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key);
      }
    }
  }

  // 获取缓存统计信息
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  // 清理定时器
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }
}

// 创建全局缓存实例
const globalForCache = globalThis as unknown as {
  cache: MemoryCache | undefined
}

export const cache = globalForCache.cache ?? new MemoryCache();

if (process.env.NODE_ENV !== 'production') {
  globalForCache.cache = cache;
}

// 缓存辅助函数
export async function getFromCache(key: string): Promise<any | null> {
  try {
    return cache.get(key);
  } catch (error) {
    console.error('Cache get error:', error);
    return null;
  }
}

export async function setToCache(key: string, data: any, ttlSeconds: number): Promise<void> {
  try {
    cache.set(key, data, ttlSeconds);
  } catch (error) {
    console.error('Cache set error:', error);
  }
}

export async function deleteFromCache(key: string): Promise<void> {
  try {
    cache.delete(key);
  } catch (error) {
    console.error('Cache delete error:', error);
  }
}

export async function clearCache(): Promise<void> {
  try {
    cache.clear();
  } catch (error) {
    console.error('Cache clear error:', error);
  }
}

// 进程退出时清理缓存
process.on('beforeExit', () => {
  cache.destroy();
});
