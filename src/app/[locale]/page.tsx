import { unstable_setRequestLocale } from 'next-intl/server';
import { getTranslations } from 'next-intl/server';
import GoogleOneTapWrapper from "@/components/GoogleOneTapWrapper";
import Home from "./home";
import { ToolCard } from '@/components/ToolCard';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Suspense } from 'react';
import { getFromCache, setToCache } from '@/lib/cache';
import { executeDbOperation } from '@/lib/db-mutex';
import { prisma } from '@/lib/prisma';


// 服务端获取工具数据，添加缓存
async function getTools(locale: string) {
  // 使用缓存键，基于locale和时间
  const cacheKey = `tools_latest_${locale}_${Math.floor(Date.now() / (1000 * 60 * 10))}`; // 10分钟缓存

  // 检查缓存
  const cachedData = await getFromCache(cacheKey);
  if (cachedData) {
    return cachedData;
  }

  // 使用互斥锁确保数据库操作串行执行
  const result = await executeDbOperation(
    async () => {
      return await prisma.$transaction(async (tx) => {
        // 串行执行查询以避免连接冲突
        const tools = await tx.tool.findMany({
          take: 20,
          orderBy: {
            updatedAt: 'desc',
          },
          select: {
            id: true,
            toolId: true,
            iconUrl: true,
            url: true,
            pricingType: true,
            isPremium: true,
            isNew: true,
            isFeatured: true,
            rating: true,
            createdAt: true,
            translations: {
              where: {
                locale,
              },
              select: {
                name: true,
                description: true,
              }
            },
            // 仅获取需要的分类信息
            categories: {
              take: 3, // 限制返回的分类数量
              select: {
                category: {
                  select: {
                    slug: true,
                    translations: {
                      where: {
                        locale,
                      },
                      select: {
                        name: true,
                      }
                    },
                  },
                },
              },
            },
          },
        });

        const total = await tx.tool.count();

        return {
          tools: tools.map((tool: any) => ({
            ...tool,
            rating: tool.rating ? parseFloat(tool.rating.toString()) : null,
            createdAt: tool.createdAt.toISOString(),
          })),
          total
        };
      }, { timeout: 30000 });
    },
    { tools: [], total: 0 }, // fallback
    'Error fetching tools'
  );

  // 存入缓存
  await setToCache(cacheKey, result, 60 * 10); // 10分钟

  return result;
}

// 根据分类slug获取分类下的工具
async function getToolsByCategory(categorySlug: string, locale: string, limit: number = 8) {
  // 使用缓存键，基于categorySlug、locale和时间
  const cacheKey = `tools_category_${categorySlug}_${locale}_${Math.floor(Date.now() / (1000 * 60 * 10))}`; // 10分钟缓存

  // 检查缓存
  const cachedData = await getFromCache(cacheKey);
  if (cachedData) {
    return cachedData;
  }

  // 使用互斥锁确保数据库操作串行执行
  const result = await executeDbOperation(
    async () => {
      return await prisma.$transaction(async (tx) => {
        // 首先获取分类信息
        const categoryData = await tx.$queryRaw<any[]>`
          SELECT c.id, c.slug, c.icon_url as "iconUrl", c.level,
                 ct.name, ct.description
          FROM categories c
          LEFT JOIN category_translations ct ON c.id = ct.category_id AND ct.locale = ${locale}
          WHERE c.slug = ${categorySlug}
        `;

        if (!categoryData || categoryData.length === 0) {
          return { tools: [], total: 0, category: null };
        }

        const category = {
          ...categoryData[0],
          name: categoryData[0].name || categorySlug,
          description: categoryData[0].description || ''
        };

        const categoryId = categoryData[0].id;
        const isParentCategory = categoryData[0].level === 1;

        // 如果是父分类，获取子分类ID
        let categoryIds = [categoryId];
        if (isParentCategory) {
          const subcategories = await tx.$queryRaw<any[]>`
            SELECT id FROM categories WHERE parent_id = ${categoryId}
          `;
          categoryIds = [...categoryIds, ...subcategories.map((sub: any) => sub.id)];
        }

        // 串行获取工具和计数
        const tools = await tx.tool.findMany({
          take: limit,
          where: {
            categories: {
              some: {
                categoryId: {
                  in: categoryIds
                }
              }
            }
          },
          orderBy: { updatedAt: 'desc' },
          select: {
            id: true,
            toolId: true,
            iconUrl: true,
            url: true,
            pricingType: true,
            isPremium: true,
            isNew: true,
            isFeatured: true,
            rating: true,
            createdAt: true,
            translations: {
              where: { locale },
              select: {
                name: true,
                description: true,
              }
            },
            // 减少关联数据加载
            categories: {
              take: 1,
              select: {
                category: {
                  select: {
                    slug: true,
                  }
                },
              },
            }
          },
        });

        const count = await tx.toolCategory.count({
          where: {
            categoryId: {
              in: categoryIds
            }
          }
        });

        // 序列化工具数据
        const serializedTools = tools.map((tool: any) => ({
          ...tool,
          rating: tool.rating ? parseFloat(tool.rating.toString()) : null,
          createdAt: tool.createdAt.toISOString(),
        }));

        return {
          tools: serializedTools,
          total: count,
          category
        };
      }, { timeout: 30000 });
    },
    { tools: [], total: 0, category: null }, // fallback
    `Error fetching tools for category ${categorySlug}`
  );

  // 存入缓存
  await setToCache(cacheKey, result, 60 * 10); // 10分钟

  return result;
}

// 使用React缓存函数缓存getTools结果
export const revalidate = 3600; // 1小时重新验证一次数据

// 工具卡片网格组件，使用Suspense包裹
function ToolsGrid({ tools, locale }: { tools: any[], locale: string }) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {tools.map((tool: any) => (
        <ToolCard key={tool.toolId} tool={tool} locale={locale} />
      ))}
    </div>
  );
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  // 使用 await 获取 locale
  const { locale } = await params;

  // 设置请求的 locale
  unstable_setRequestLocale(locale);

  // 获取国际化翻译
  const t = await getTranslations('tools');

  // 串行获取数据以避免数据库连接冲突
  let latestToolsData, writingCategoryData, imageCategoryData, videoCategoryData, voiceCategoryData;

  try {
    // 串行执行，避免并发连接问题
    console.log('Fetching latest tools...');
    latestToolsData = await getTools(locale);

    console.log('Fetching writing category...');
    writingCategoryData = await getToolsByCategory('text-writing', locale);

    console.log('Fetching image category...');
    imageCategoryData = await getToolsByCategory('image', locale);

    console.log('Fetching video category...');
    videoCategoryData = await getToolsByCategory('video', locale);

    console.log('Fetching voice category...');
    voiceCategoryData = await getToolsByCategory('voice', locale);

    console.log('All data fetched successfully');
  } catch (error) {
    console.error('Error fetching page data:', error);
    // 提供默认数据以防止页面崩溃
    latestToolsData = { tools: [], total: 0 };
    writingCategoryData = { tools: [], total: 0, category: null };
    imageCategoryData = { tools: [], total: 0, category: null };
    videoCategoryData = { tools: [], total: 0, category: null };
    voiceCategoryData = { tools: [], total: 0, category: null };
  }

  // 解构数据
  const { tools, total } = latestToolsData;
  const { tools: writingTools, category: writingCategory } = writingCategoryData;
  const { tools: imageTools, category: imageCategory } = imageCategoryData;
  const { tools: videoTools, category: videoCategory } = videoCategoryData;
  const { tools: voiceTools, category: voiceCategory } = voiceCategoryData;
  
  // 计算是否有更多工具可加载
  const hasMoreTools = total > tools.length;

  return (
    <>
      {/* Google One Tap组件 */}
      <GoogleOneTapWrapper />
      
      {/* Hero区域和搜索 */}
      <Home />
      
      {/* 导航标签区域 */}
      <div className="sticky z-[5] border-b border-border bg-background">
        <div className="max-w-11xl px-4 pt-5 pb-4 mx-auto">
          <div className="flex overflow-x-auto py-2 gap-4 no-scrollbar">
            <Link href={`/${locale}/tools`} className="px-4 py-2 whitespace-nowrap rounded-full bg-secondary text-secondary-foreground hover:bg-accent hover:text-accent-foreground transition-colors">
              {t('allTools', { defaultValue: '全部工具' })}
            </Link>
            
            <Link href={`/${locale}/tools?sort=newest-registered`} className="px-4 py-2 whitespace-nowrap rounded-full bg-secondary text-secondary-foreground hover:bg-accent hover:text-accent-foreground transition-colors flex items-center gap-1">
              <span className="relative flex h-2 w-2">
                <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
              </span>
              {t('latestRegisteredTools', { defaultValue: '最新注册' })}
            </Link>
            
            {writingCategory && (
              <Link href={`/${locale}/tools?category=text-writing`} className="px-4 py-2 whitespace-nowrap rounded-full bg-secondary text-secondary-foreground hover:bg-accent hover:text-accent-foreground transition-colors">
                {writingCategory.name}
              </Link>
            )}
            
            {imageCategory && (
              <Link href={`/${locale}/tools?category=image`} className="px-4 py-2 whitespace-nowrap rounded-full bg-secondary text-secondary-foreground hover:bg-accent hover:text-accent-foreground transition-colors">
                {imageCategory.name}
              </Link>
            )}
            
            {videoCategory && (
              <Link href={`/${locale}/tools?category=video`} className="px-4 py-2 whitespace-nowrap rounded-full bg-secondary text-secondary-foreground hover:bg-accent hover:text-accent-foreground transition-colors">
                {videoCategory.name}
              </Link>
            )}
            
            {voiceCategory && (
              <Link href={`/${locale}/tools?category=voice`} className="px-4 py-2 whitespace-nowrap rounded-full bg-secondary text-secondary-foreground hover:bg-accent hover:text-accent-foreground transition-colors">
                {voiceCategory.name}
              </Link>
            )}
          </div>
        </div>
      </div>

      {/* 添加一个空的div占位元素，高度等于导航栏高度 */}
      <div className="h-[81px]"></div>

      <div className="container mx-auto px-4 pb-9 max-w-11xl">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold mb-6">{t('latestTools')}</h1>
          <div className="flex justify-center mt-8">
            <Link href={`/${locale}/tools`} passHref>
              <Button className="bg-transparent hover:bg-primary text-primary hover:text-white border border-primary px-6 py-2 rounded-lg transition-all duration-300 flex items-center gap-2">
                {t('viewMore')}
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
              </Button>
            </Link>
          </div>
        </div>
        
        {tools.length === 0 ? (
          <div className="flex justify-center items-center h-64">
            <p className="text-gray-500">{t('noTools', { defaultValue: '暂无工具数据' })}</p>
          </div>
        ) : (
          <>
            <Suspense fallback={
              <div className="animate-pulse grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {[...Array(8)].map((_, index) => (
                  <div key={index} className="bg-gray-100 rounded-lg h-64"></div>
                ))}
              </div>
            }>
              <ToolsGrid tools={tools} locale={locale} />
            </Suspense>
          </>
        )}
        
        {/* 文字写作分类工具展示区域 */}
        {writingCategory && (
          <div className="mt-16">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-3xl font-bold">{writingCategory.name}</h2>
              <div className="flex justify-center mt-8">
                <Link href={`/${locale}/tools?category=text-writing`} passHref>
                  <Button className="bg-transparent hover:bg-primary text-primary hover:text-white border border-primary px-6 py-2 rounded-lg transition-all duration-300 flex items-center gap-2">
                    {t('viewMore')}
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                  </Button>
                </Link>
              </div>
            </div>
            
            {writingTools.length === 0 ? (
              <div className="flex justify-center items-center h-64">
                <p className="text-gray-500">{t('noTools', { defaultValue: '暂无工具数据' })}</p>
              </div>
            ) : (
              <>
                <Suspense fallback={
                  <div className="animate-pulse grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    {[...Array(8)].map((_, index) => (
                      <div key={index} className="bg-gray-100 rounded-lg h-64"></div>
                    ))}
                  </div>
                }>
                  <ToolsGrid tools={writingTools} locale={locale} />
                </Suspense>
              </>
            )}
          </div>
        )}
        
        {/* 图片分类工具展示区域 */}
        {imageCategory && (
          <div className="mt-16">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-3xl font-bold">{imageCategory.name}</h2>
              <div className="flex justify-center mt-8">
                <Link href={`/${locale}/tools?category=image`} passHref>
                  <Button className="bg-transparent hover:bg-primary text-primary hover:text-white border border-primary px-6 py-2 rounded-lg transition-all duration-300 flex items-center gap-2">
                    {t('viewMore')}
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                  </Button>
                </Link>
              </div>
            </div>
            
            {imageTools.length === 0 ? (
              <div className="flex justify-center items-center h-64">
                <p className="text-gray-500">{t('noTools', { defaultValue: '暂无工具数据' })}</p>
              </div>
            ) : (
              <>
                <Suspense fallback={
                  <div className="animate-pulse grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    {[...Array(8)].map((_, index) => (
                      <div key={index} className="bg-gray-100 rounded-lg h-64"></div>
                    ))}
                  </div>
                }>
                  <ToolsGrid tools={imageTools} locale={locale} />
                </Suspense>
              </>
            )}
          </div>
        )}
        
        {/* 视频分类工具展示区域 */}
        {videoCategory && (
          <div className="mt-16">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-3xl font-bold">{videoCategory.name}</h2>
              <div className="flex justify-center mt-8">
                <Link href={`/${locale}/tools?category=video`} passHref>
                  <Button className="bg-transparent hover:bg-primary text-primary hover:text-white border border-primary px-6 py-2 rounded-lg transition-all duration-300 flex items-center gap-2">
                    {t('viewMore')}
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                  </Button>
                </Link>
              </div>
            </div>
            
            {videoTools.length === 0 ? (
              <div className="flex justify-center items-center h-64">
                <p className="text-gray-500">{t('noTools', { defaultValue: '暂无工具数据' })}</p>
              </div>
            ) : (
              <>
                <Suspense fallback={
                  <div className="animate-pulse grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    {[...Array(8)].map((_, index) => (
                      <div key={index} className="bg-gray-100 rounded-lg h-64"></div>
                    ))}
                  </div>
                }>
                  <ToolsGrid tools={videoTools} locale={locale} />
                </Suspense>
              </>
            )}
          </div>
        )}
        
        {/* 声音分类工具展示区域 */}
        {voiceCategory && (
          <div className="mt-16">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-3xl font-bold">{voiceCategory.name}</h2>
              <div className="flex justify-center mt-8">
                <Link href={`/${locale}/tools?category=voice`} passHref>
                  <Button className="bg-transparent hover:bg-primary text-primary hover:text-white border border-primary px-6 py-2 rounded-lg transition-all duration-300 flex items-center gap-2">
                    {t('viewMore')}
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                  </Button>
                </Link>
              </div>
            </div>
            
            {voiceTools.length === 0 ? (
              <div className="flex justify-center items-center h-64">
                <p className="text-gray-500">{t('noTools', { defaultValue: '暂无工具数据' })}</p>
              </div>
            ) : (
              <>
                <Suspense fallback={
                  <div className="animate-pulse grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    {[...Array(8)].map((_, index) => (
                      <div key={index} className="bg-gray-100 rounded-lg h-64"></div>
                    ))}
                  </div>
                }>
                  <ToolsGrid tools={voiceTools} locale={locale} />
                </Suspense>
              </>
            )}
          </div>
        )}
      </div>
    </>
  );
}


